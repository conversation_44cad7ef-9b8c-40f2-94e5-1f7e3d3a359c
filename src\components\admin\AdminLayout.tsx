import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';

interface AdminLayoutProps {
  children: React.ReactNode;
  title: string;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children, title }) => {
  const router = useRouter();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  // دالة للتحقق من تسجيل الدخول
  const checkAuth = () => {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('authToken') ||
                   document.cookie.split('; ').find(row => row.startsWith('authToken='))?.split('=')[1];
      return !!token;
    }
    return false;
  };

  useEffect(() => {
    setMounted(true);
    if (!checkAuth()) {
      router.push('/admin/login');
    }
  }, [router]);

  const handleLogout = async () => {
    try {
      // استدعاء API تسجيل الخروج
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include'
      });

      // حذف التوكن من التخزين المحلي
      if (typeof window !== 'undefined') {
        localStorage.removeItem('authToken');
        document.cookie = 'authToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
      }

      router.push('/admin/login');
    } catch (error) {
      console.error('Logout error:', error);
      // حذف التوكن حتى لو فشل الطلب
      if (typeof window !== 'undefined') {
        localStorage.removeItem('authToken');
        document.cookie = 'authToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
      }
      router.push('/admin/login');
    }
  };

  const menuItems = [
    {
      title: 'لوحة التحكم',
      icon: 'ri-dashboard-line',
      href: '/admin/dashboard',
      active: router.pathname === '/admin/dashboard'
    },
    {
      title: 'الفئات الرئيسية',
      icon: 'ri-folder-line',
      href: '/admin/categories',
      active: router.pathname === '/admin/categories'
    },
    {
      title: 'الفئات الفرعية',
      icon: 'ri-folder-2-line',
      href: '/admin/subcategories',
      active: router.pathname === '/admin/subcategories'
    },
    {
      title: 'المنتجات',
      icon: 'ri-product-hunt-line',
      href: '/admin/products',
      active: router.pathname === '/admin/products'
    },
    {
      title: 'طلبات التسعير',
      icon: 'ri-file-text-line',
      href: '/admin/quote-requests',
      active: router.pathname === '/admin/quote-requests'
    },
    {
      title: 'إعدادات الموقع',
      icon: 'ri-settings-line',
      href: '/admin/settings',
      active: router.pathname === '/admin/settings'
    },
    {
      title: 'إعدادات الحساب',
      icon: 'ri-user-settings-line',
      href: '/admin/account-settings',
      active: router.pathname === '/admin/account-settings'
    }
  ];

  if (!mounted) {
    return (
      <div className="min-h-screen bg-[#F8FAFC] flex items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-[#3B82F6] border-t-transparent"></div>
          <p className="mt-4 text-[#64748B] font-medium">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#F8FAFC] flex" dir="rtl">
      {/* Sidebar */}
      <div 
        className={`fixed inset-y-0 right-0 z-50 w-80 bg-white shadow-[0_0_50px_rgba(0,0,0,0.05)] transform ${
          sidebarOpen ? 'translate-x-0' : 'translate-x-full'
        } transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`}
      >
        <div className="flex items-center justify-between h-24 px-8 bg-white border-b border-[#E2E8F0]">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-[#3B82F6] rounded-2xl flex items-center justify-center ml-4 shadow-lg shadow-[#3B82F6]/20">
              <i className="ri-dashboard-3-line text-white text-2xl"></i>
            </div>
            <h1 className="text-[#1E293B] text-2xl font-bold">لوحة التحكم</h1>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden text-[#64748B] hover:text-[#3B82F6] p-2 rounded-xl hover:bg-[#F1F5F9] transition-all duration-200"
          >
            <i className="ri-close-line text-2xl"></i>
          </button>
        </div>

        <nav className="mt-8 px-6">
          {menuItems.map((item, index) => (
            <Link
              key={index}
              href={item.href}
              className={`sidebar-item flex items-center px-6 py-4 mb-3 rounded-2xl text-[#64748B] hover:bg-[#F1F5F9] hover:text-[#3B82F6] transition-all duration-300 ${
                item.active 
                  ? 'bg-[#EFF6FF] text-[#3B82F6] border-r-4 border-[#3B82F6]' 
                  : ''
              }`}
            >
              <div className={`w-12 h-12 rounded-2xl flex items-center justify-center ml-4 ${
                item.active 
                  ? 'bg-[#DBEAFE] text-[#3B82F6]' 
                  : 'bg-[#F1F5F9] text-[#64748B]'
              }`}>
                <i className={`${item.icon} text-xl`}></i>
              </div>
              <span className="font-medium text-lg">{item.title}</span>
            </Link>
          ))}
        </nav>

        <div className="absolute bottom-0 w-full p-6">
          <button
            onClick={handleLogout}
            className="flex items-center w-full px-6 py-4 text-[#EF4444] hover:bg-[#FEF2F2] rounded-2xl transition-all duration-300"
          >
            <div className="w-12 h-12 rounded-2xl bg-[#FEE2E2] flex items-center justify-center ml-4">
              <i className="ri-logout-box-line text-xl text-[#EF4444]"></i>
            </div>
            <span className="font-medium text-lg">تسجيل الخروج</span>
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 lg:mr">
        {/* Top Bar */}
        <header className="bg-white shadow-[0_0_50px_rgba(0,0,0,0.05)] border-b border-[#E2E8F0] sticky top-0 z-40">
          <div className="flex items-center justify-between h-24 px-8">
            <div className="flex items-center">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden text-[#64748B] hover:text-[#3B82F6] p-2 rounded-xl hover:bg-[#F1F5F9] transition-all duration-200"
              >
                <i className="ri-menu-line text-2xl"></i>
              </button>
              <div className="flex items-center mr-6">
                <div className="w-12 h-12 bg-[#3B82F6] rounded-2xl flex items-center justify-center ml-4 shadow-lg shadow-[#3B82F6]/20">
                  <i className="ri-dashboard-line text-white text-xl"></i>
                </div>
                <h2 className="text-2xl font-bold text-[#1E293B]">{title}</h2>
              </div>
            </div>

            <div className="flex items-center space-x-6 space-x-reverse">
              <Link
                href="/"
                target="_blank"
                className="text-[#64748B] hover:text-[#3B82F6] p-3 rounded-xl hover:bg-[#F1F5F9] transition-all duration-200"
                title="عرض الموقع"
              >
                <i className="ri-external-link-line text-2xl"></i>
              </Link>
              <div className="flex items-center space-x-4 space-x-reverse bg-[#F1F5F9] px-6 py-3 rounded-2xl">
                <div className="w-10 h-10 bg-[#3B82F6] rounded-xl flex items-center justify-center shadow-lg shadow-[#3B82F6]/20">
                  <i className="ri-user-line text-white text-lg"></i>
                </div>
                <span className="text-base text-[#1E293B] font-medium">المدير</span>
              </div>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="p-8">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>

      {/* Sidebar Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-[#1E293B]/20 backdrop-blur-sm z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        ></div>
      )}
    </div>
  );
};

export default AdminLayout;
