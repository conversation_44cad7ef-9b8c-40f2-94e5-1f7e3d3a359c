import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'VidMeet - معدات الضيافة',
  description: 'موقع متخصص في معدات المطاعم والفنادق',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // إخفاء تحذيرات React المتعلقة بـ sticky/fixed positioning
  if (typeof window !== 'undefined') {
    const originalConsoleWarn = console.warn;
    console.warn = (...args) => {
      if (args[0]?.includes?.('Skipping auto-scroll behavior due to')) {
        return;
      }
      originalConsoleWarn.apply(console, args);
    };
  }

  return (
    <html>
      <head>
        <link
          href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap"
          rel="stylesheet"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap"
          rel="stylesheet"
        />
        <link
          href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css"
          rel="stylesheet"
        />
      </head>
      <body className={inter.className} suppressHydrationWarning={true}>
        {children}
      </body>
    </html>
  )
}
