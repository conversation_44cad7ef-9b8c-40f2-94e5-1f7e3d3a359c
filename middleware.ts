import { NextRequest, NextResponse } from 'next/server';
import { locales, defaultLocale, isValidLocale } from './lib/i18n';

// إعدادات الأمان
const SECURITY_HEADERS = {
  // منع تضمين الصفحة في iframe
  'X-Frame-Options': 'DENY',
  // منع تخمين نوع المحتوى
  'X-Content-Type-Options': 'nosniff',
  // حماية من XSS
  'X-XSS-Protection': '1; mode=block',
  // فرض HTTPS (في الإنتاج)
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  // منع تسريب المرجع
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  // إزالة معلومات الخادم
  'X-Powered-By': '',
  // Content Security Policy
  'Content-Security-Policy': [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'", // مؤقت للتطوير
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: blob:",
    "font-src 'self'",
    "connect-src 'self'",
    "media-src 'self'",
    "object-src 'none'",
    "child-src 'none'",
    "worker-src 'self'",
    "frame-ancestors 'none'",
    "form-action 'self'",
    "base-uri 'self'"
  ].join('; ')
};

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // إنشاء الاستجابة
  let response = NextResponse.next();

  // تجاهل الملفات الثابتة والـ API routes والـ admin للتوجيه
  const shouldSkipLocaleRedirect = (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/uploads') ||
    pathname.startsWith('/admin') ||
    pathname.includes('.')
  );

  if (!shouldSkipLocaleRedirect) {

    // تحقق من وجود locale في المسار
    const pathnameIsMissingLocale = locales.every(
      (locale) => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`
    );

    // إذا كان المسار لا يحتوي على locale، أضف الـ locale المناسب
    if (pathnameIsMissingLocale) {
      // تحقق من تفضيل المستخدم في الكوكيز
      const preferredLocale = request.cookies.get('preferredLocale')?.value;

      // إذا لم يكن هناك تفضيل محفوظ، استخدم الافتراضي
      const targetLocale = preferredLocale && isValidLocale(preferredLocale)
        ? preferredLocale
        : defaultLocale;

      response = NextResponse.redirect(
        new URL(`/${targetLocale}${pathname === '/' ? '' : pathname}`, request.url)
      );
    }
  }

  // إضافة headers الأمان لجميع الاستجابات
  Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  // إضافة headers خاصة بالبيئة
  if (process.env.NODE_ENV === 'production') {
    // في الإنتاج، فرض HTTPS
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  } else {
    // في التطوير، headers أقل صرامة
    response.headers.set('Strict-Transport-Security', 'max-age=0');
  }

  // إضافة header مخصص للتطبيق
  response.headers.set('X-App-Version', '2.0-secure');
  response.headers.set('X-Security-Level', 'enhanced');

  return response;
}

export const config = {
  matcher: [
    // تطبيق على جميع المسارات عدا:
    '/((?!api|_next/static|_next/image|favicon.ico|uploads).*)',
  ],
};
