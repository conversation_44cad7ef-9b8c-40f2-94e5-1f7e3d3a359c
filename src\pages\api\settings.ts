import { NextApiRequest, NextApiResponse } from 'next';
import { getSiteSettings, updateSiteSettings } from '../../lib/database';
import { requireAdminAuth } from '../../lib/auth';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // التحقق من المصادقة والصلاحيات للعمليات التي تتطلب تعديل
  if (req.method !== 'GET') {
    const user = requireAdminAuth(req);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        messageAr: 'المصادقة مطلوبة'
      });
    }
  }
  try {
    switch (req.method) {
      case 'GET':
        const settings = getSiteSettings();
        res.status(200).json(settings);
        break;

      case 'PUT':
        const updates = req.body;
        updateSiteSettings(updates);
        const updatedSettings = getSiteSettings();
        res.status(200).json(updatedSettings);
        break;

      default:
        res.setHeader('Allow', ['GET', 'PUT']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
}
